import type { LoaderFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { useNavigate } from "@remix-run/react";
import { TitleBar } from "@shopify/app-bridge-react";
import { authenticate } from "../shopify.server";
import { BillingService } from "../services/billing.server";

// Optimized static icons
const OptimizeIcon = ({ size = 48 }: { size?: number }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" fill="currentColor">
    <path d="M12 2L15.09 8.26L22 9L17 14L18.18 21L12 17.77L5.82 21L7 14L2 9L8.91 8.26L12 2Z" />
  </svg>
);

const BillingIcon = ({ size = 48 }: { size?: number }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" fill="currentColor">
    <rect x="2" y="6" width="20" height="12" rx="2" />
    <path d="M6 10H10V14H6V10Z" fill="white" />
    <line x1="14" y1="11" x2="18" y2="11" stroke="white" strokeWidth="2" />
    <line x1="14" y1="13" x2="16" y2="13" stroke="white" strokeWidth="2" />
  </svg>
);

const SettingsIcon = ({ size = 48 }: { size?: number }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" fill="currentColor">
    <path d="M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15Z" />
    <path d="M19.4 15C19.2669 15.3016 19.2272 15.6362 19.286 15.9606C19.3448 16.285 19.4995 16.5843 19.73 16.82L19.79 16.88C19.976 17.0657 20.1235 17.2863 20.2241 17.5291C20.3248 17.7719 20.3766 18.0322 20.3766 18.295C20.3766 18.5578 20.3248 18.8181 20.2241 19.0609C20.1235 19.3037 19.976 19.5243 19.79 19.71C19.6043 19.896 19.3837 20.0435 19.1409 20.1441C18.8981 20.2448 18.6378 20.2966 18.375 20.2966C18.1122 20.2966 17.8519 20.2448 17.6091 20.1441C17.3663 20.0435 17.1457 19.896 16.96 19.71L16.9 19.65C16.6643 19.4195 16.365 19.2648 16.0406 19.206C15.7162 19.1472 15.3816 19.1869 15.08 19.32C14.7842 19.4468 14.532 19.6572 14.3543 19.9255C14.1766 20.1938 14.0813 20.5082 14.08 20.83V21C14.08 21.5304 13.8693 22.0391 13.4942 22.4142C13.1191 22.7893 12.6104 23 12.08 23C11.5496 23 11.0409 22.7893 10.6658 22.4142C10.2907 22.0391 10.08 21.5304 10.08 21V20.91C10.0723 20.579 9.96512 20.258 9.77251 19.9887C9.5799 19.7194 9.31074 19.5143 9 19.4C8.69838 19.2669 8.36381 19.2272 8.03941 19.286C7.71502 19.3448 7.41568 19.4995 7.18 19.73L7.12 19.79C6.93425 19.976 6.71368 20.1235 6.47088 20.2241C6.22808 20.3248 5.96783 20.3766 5.705 20.3766C5.44217 20.3766 5.18192 20.3248 4.93912 20.2241C4.69632 20.1235 4.47575 19.976 4.29 19.79C4.10405 19.6043 3.95653 19.3837 3.85588 19.1409C3.75523 18.8981 3.70343 18.6378 3.70343 18.375C3.70343 18.1122 3.75523 17.8519 3.85588 17.6091C3.95653 17.3663 4.10405 17.1457 4.29 16.96L4.35 16.9C4.58054 16.6643 4.73519 16.365 4.794 16.0406C4.85282 15.7162 4.81312 15.3816 4.68 15.08C4.55324 14.7842 4.34276 14.532 4.07447 14.3543C3.80618 14.1766 3.49179 14.0813 3.17 14.08H3C2.46957 14.08 1.96086 13.8693 1.58579 13.4942C1.21071 13.1191 1 12.6104 1 12.08C1 11.5496 1.21071 11.0409 1.58579 10.6658C1.96086 10.2907 2.46957 10.08 3 10.08H3.09C3.42099 10.0723 3.742 9.96512 4.0113 9.77251C4.28059 9.5799 4.48572 9.31074 4.6 9C4.73312 8.69838 4.77282 8.36381 4.714 8.03941C4.65519 7.71502 4.50054 7.41568 4.27 7.18L4.21 7.12C4.02405 6.93425 3.87653 6.71368 3.77588 6.47088C3.67523 6.22808 3.62343 5.96783 3.62343 5.705C3.62343 5.44217 3.67523 5.18192 3.77588 4.93912C3.87653 4.69632 4.02405 4.47575 4.21 4.29C4.39575 4.10405 4.61632 3.95653 4.85912 3.85588C5.10192 3.75523 5.36217 3.70343 5.625 3.70343C5.88783 3.70343 6.14808 3.75523 6.39088 3.85588C6.63368 3.95653 6.85425 4.10405 7.04 4.29L7.1 4.35C7.33568 4.58054 7.63502 4.73519 7.95941 4.794C8.28381 4.85282 8.61838 4.81312 8.92 4.68H9C9.29577 4.55324 9.54802 4.34276 9.72569 4.07447C9.90337 3.80618 9.99872 3.49179 10 3.17V3C10 2.46957 10.2107 1.96086 10.5858 1.58579C10.9609 1.21071 11.4696 1 12 1C12.5304 1 13.0391 1.21071 13.4142 1.58579C13.7893 1.96086 14 2.46957 14 3V3.09C14.0013 3.41179 14.0966 3.72618 14.2743 3.99447C14.452 4.26276 14.7042 4.47324 15 4.6C15.3016 4.73312 15.6362 4.77282 15.9606 4.714C16.285 4.65519 16.5843 4.50054 16.82 4.27L16.88 4.21C17.0657 4.02405 17.2863 3.87653 17.5291 3.77588C17.7719 3.67523 18.0322 3.62343 18.295 3.62343C18.5578 3.62343 18.8181 3.67523 19.0609 3.77588C19.3037 3.87653 19.5243 4.02405 19.71 4.21C19.896 4.39575 20.0435 4.61632 20.1441 4.85912C20.2448 5.10192 20.2966 5.36217 20.2966 5.625C20.2966 5.88783 20.2448 6.14808 20.1441 6.39088C20.0435 6.63368 19.896 6.85425 19.71 7.04L19.65 7.1C19.4195 7.33568 19.2648 7.63502 19.206 7.95941C19.1472 8.28381 19.1869 8.61838 19.32 8.92V9C19.4468 9.29577 19.6572 9.54802 19.9255 9.72569C20.1938 9.90337 20.5082 9.99872 20.83 10H21C21.5304 10 22.0391 10.2107 22.4142 10.5858C22.7893 10.9609 23 11.4696 23 12C23 12.5304 22.7893 13.0391 22.4142 13.4142C22.0391 13.7893 21.5304 14 21 14H20.91C20.5882 14.0013 20.2738 14.0966 20.0055 14.2743C19.7372 14.452 19.5268 14.7042 19.4 15Z" />
  </svg>
);
import { Button, Spinner } from "@shopify/polaris";
import * as React from "react";

interface ProductSeoData {
  id: string;
  title: string;
  description: string;
  type: string;
  vendor: string;
  seoTitle: string;
  seoDescription: string;
  handle: string;
  seoScore: number;
  status: 'pending' | 'processing' | 'optimized' | 'failed';
  lastOptimized?: string;
  viralKeyword?: string;
  targetKeywords?: string[];
}

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { admin, session } = await authenticate.admin(request);

  // Setup billing webhooks on first load (optional - can also do in Partner Dashboard)
  // await setupAppBilling(admin);

  // Get billing information
  const billingService = new BillingService(admin, session.shop);
  const billingStatus = await billingService.hasActiveBilling();

  // Return immediately with empty data for instant loading
  return json({
    products: [] as ProductSeoData[],
    totalProducts: 0,
    isInitialLoad: true,
    billing: {
      hasAccess: billingStatus.hasAccess,
      plan: billingStatus.plan,
      subscription: billingStatus.subscription,
      trialExpired: billingStatus.trialExpired
    }
  });
};



export default function Dashboard() {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);

  const retryConnection = () => {
    setError(null);
    setIsLoading(true);
    // Simulate a connection attempt
    setTimeout(() => {
      setIsLoading(false);
    }, 2000);
  };

  return (
    <>
      <TitleBar title="SEO Dashboard" />

      {/* Optimized Full Height Hero Section with Proper Padding */}
      <div className="min-h-screen bg-black text-white flex items-center justify-center px-6 py-20 relative">
        {/* Simple static background */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-white rounded-full blur-3xl" />
          <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-white rounded-full blur-2xl" />
        </div>

        <div className="relative z-10 w-full max-w-7xl mx-auto">
          {/* Optimized Header with Logo */}
          <div className="text-center mb-20">
            {/* Logo and Badge */}
            <div className="flex flex-col items-center mb-12">
              <img
                src="/logo.png"
                alt="ProdRankX Logo"
                className="w-16 h-16 mb-6 rounded-2xl shadow-2xl"
                style={{ filter: 'brightness(1.1) contrast(1.1)' }}
              />
              <div className="inline-flex items-center bg-white/10 rounded-full px-8 py-4 border border-white/20">
                <div className="w-3 h-3 bg-white rounded-full mr-4" />
                <span className="text-sm font-semibold tracking-widest uppercase">SEO Dashboard</span>
              </div>
            </div>

            <h1 style={{
              fontSize: 'clamp(4rem, 12vw, 12rem)',
              fontWeight: 900,
              lineHeight: 0.85,
              letterSpacing: '-0.08em',
              marginBottom: '2rem',
              color: 'white'
            }}>
              PRODRANK
              <br />
              <span style={{ color: '#64748b' }}>X</span>
            </h1>

            <p style={{
              fontSize: 'clamp(1.5rem, 4vw, 2.5rem)',
              fontWeight: 300,
              color: '#cbd5e1',
              lineHeight: 1.4,
              maxWidth: '60rem',
              margin: '0 auto 1.5rem auto'
            }}>
              Transform your Shopify store with AI-powered SEO optimization
            </p>

            <p style={{
              fontSize: 'clamp(1.125rem, 2.5vw, 1.5rem)',
              fontWeight: 400,
              color: '#94a3b8',
              maxWidth: '40rem',
              margin: '0 auto',
              letterSpacing: '0.05em'
            }}>
              Boost rankings • Drive traffic • Increase conversions
            </p>
          </div>

          {/* Test Mode Banner */}
          <div className="max-w-7xl mx-auto mb-8">
            <div className="bg-yellow-500/20 border border-yellow-500/40 rounded-2xl p-4 text-center">
              <div className="text-yellow-300 font-bold text-lg">🧪 TEST MODE ENABLED</div>
              <div className="text-yellow-200 text-sm mt-1">All payments are in test mode - no real charges will be made</div>
            </div>
          </div>

          {/* Optimized Navigation Grid */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-10 max-w-7xl mx-auto">
            {/* Product Optimizer */}
            <div
              className="group cursor-pointer transform hover:scale-105 transition-transform duration-200"
              onClick={() => navigate("/app/seo-dashboard")}
            >
              <div className="bg-white/10 border border-white/20 rounded-3xl p-12 hover:bg-white/15 hover:border-white/30 transition-all duration-300 h-full">
                <div className="text-center">
                  <div className="w-24 h-24 bg-white rounded-3xl mx-auto mb-10 flex items-center justify-center text-black">
                    <OptimizeIcon size={48} />
                  </div>

                  <h3 style={{
                    fontSize: 'clamp(1.75rem, 3vw, 2.25rem)',
                    fontWeight: 800,
                    marginBottom: '1.5rem',
                    color: 'white',
                    letterSpacing: '-0.02em'
                  }}>
                    Product Optimizer
                  </h3>

                  <p style={{
                    fontSize: 'clamp(1.125rem, 2vw, 1.25rem)',
                    fontWeight: 300,
                    color: '#cbd5e1',
                    lineHeight: 1.8,
                    marginBottom: '2.5rem',
                    letterSpacing: '0.02em'
                  }}>
                    Bulk optimize titles, descriptions, and SEO meta tags with AI-powered content generation and viral keywords
                  </p>

                  <Button
                    size="large"
                    onClick={() => navigate('/app/seo-dashboard')}
                  >
                    Start Optimizing →
                  </Button>
                </div>
              </div>
            </div>

            {/* Billing & Plans */}
            <div
              className="group cursor-pointer transform hover:scale-105 transition-transform duration-200"
              onClick={() => navigate("/app/billing")}
            >
              <div className="bg-white/10 border border-white/20 rounded-3xl p-12 hover:bg-white/15 hover:border-white/30 transition-all duration-300 h-full">
                <div className="text-center">
                  <div className="w-24 h-24 bg-white rounded-3xl mx-auto mb-10 flex items-center justify-center text-black">
                    <BillingIcon size={48} />
                  </div>

                  <h3 style={{
                    fontSize: 'clamp(1.75rem, 3vw, 2.25rem)',
                    fontWeight: 800,
                    marginBottom: '1.5rem',
                    color: 'white',
                    letterSpacing: '-0.02em'
                  }}>
                    Billing & Plans
                  </h3>

                  <p style={{
                    fontSize: 'clamp(1.125rem, 2vw, 1.25rem)',
                    fontWeight: 300,
                    color: '#cbd5e1',
                    lineHeight: 1.8,
                    marginBottom: '2.5rem',
                    letterSpacing: '0.02em'
                  }}>
                    Manage subscriptions, view usage analytics, and upgrade your plan to unlock premium features
                  </p>

                  <Button
                    size="large"
                    onClick={() => navigate('/app/billing')}
                  >
                    Manage Billing →
                  </Button>
                </div>
              </div>
            </div>

            {/* Settings */}
            <div
              className="group cursor-pointer transform hover:scale-105 transition-transform duration-200"
              onClick={() => navigate("/app/settings")}
            >
              <div className="bg-white/10 border border-white/20 rounded-3xl p-12 hover:bg-white/15 hover:border-white/30 transition-all duration-300 h-full">
                <div className="text-center">
                  <div className="w-24 h-24 bg-white rounded-3xl mx-auto mb-10 flex items-center justify-center text-black">
                    <SettingsIcon size={48} />
                  </div>

                  <h3 style={{
                    fontSize: 'clamp(1.75rem, 3vw, 2.25rem)',
                    fontWeight: 800,
                    marginBottom: '1.5rem',
                    color: 'white',
                    letterSpacing: '-0.02em'
                  }}>
                    Settings
                  </h3>

                  <p style={{
                    fontSize: 'clamp(1.125rem, 2vw, 1.25rem)',
                    fontWeight: 300,
                    color: '#cbd5e1',
                    lineHeight: 1.8,
                    marginBottom: '2.5rem',
                    letterSpacing: '0.02em'
                  }}>
                    Configure SEO preferences, API settings, and customize your optimization workflows
                  </p>

                  <Button
                    size="large"
                    onClick={() => navigate('/app/settings')}
                  >
                    Open Settings →
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Optimized Loading/Error States */}
          {isLoading && (
            <div className="mt-20 text-center">
              <div className="bg-white/10 border border-white/20 rounded-3xl p-12 max-w-md mx-auto">
                <Spinner accessibilityLabel="Loading dashboard..." size="large" />
                <p className="text-slate-300 mt-6 text-lg">Preparing your SEO workspace...</p>
              </div>
            </div>
          )}

          {error && (
            <div className="mt-20 max-w-lg mx-auto">
              <div className="bg-white/10 border border-red-500/30 rounded-3xl p-12 text-center">
                <div className="w-16 h-16 bg-red-500/20 rounded-2xl mx-auto mb-6 flex items-center justify-center">
                  <div className="w-8 h-8 border-2 border-red-400 rounded-full border-t-transparent animate-spin" />
                </div>
                <h3 className="text-2xl font-bold mb-4 text-red-400">Connection Error</h3>
                <p className="text-slate-300 mb-8 text-lg leading-relaxed">{error}</p>
                <Button
                  onClick={retryConnection}
                  size="large"
                >
                  Retry Connection
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );
}
