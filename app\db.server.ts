import { PrismaClient } from "@prisma/client";

declare global {
  var prismaGlobal: EnhancedPrismaClient | undefined;
}

// Enhanced Prisma client with proper connection handling
class EnhancedPrismaClient extends PrismaClient {
  private static instance: EnhancedPrismaClient;
  private connectionAttempts = 0;
  private maxRetries = 3;

  constructor() {
    super({
      log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],
      errorFormat: 'pretty',
    });
  }

  static getInstance(): EnhancedPrismaClient {
    if (!EnhancedPrismaClient.instance) {
      EnhancedPrismaClient.instance = new EnhancedPrismaClient();
    }
    return EnhancedPrismaClient.instance;
  }

  async connectWithRetry(): Promise<void> {
    try {
      await this.$connect();
      console.log('✅ Database connected successfully');
      this.connectionAttempts = 0;
    } catch (error) {
      this.connectionAttempts++;
      console.error(`❌ Database connection attempt ${this.connectionAttempts} failed:`, error);

      if (this.connectionAttempts < this.maxRetries) {
        const delay = Math.pow(2, this.connectionAttempts) * 1000; // Exponential backoff
        console.log(`⏳ Retrying connection in ${delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
        return this.connectWithRetry();
      } else {
        throw new Error(`Failed to connect to database after ${this.maxRetries} attempts`);
      }
    }
  }

  async safeQuery<T>(operation: () => Promise<T>): Promise<T> {
    try {
      return await operation();
    } catch (error) {
      console.error('❌ Database query failed:', error);

      // Check if it's a connection error and retry once
      if (error instanceof Error && error.message.includes('connection')) {
        console.log('🔄 Attempting to reconnect and retry query...');
        await this.connectWithRetry();
        return await operation();
      }

      throw error;
    }
  }
}

// Global instance management
let prisma: EnhancedPrismaClient;

if (process.env.NODE_ENV === "production") {
  prisma = EnhancedPrismaClient.getInstance();
} else {
  if (!global.prismaGlobal) {
    global.prismaGlobal = EnhancedPrismaClient.getInstance();
  }
  prisma = global.prismaGlobal;
}

// Initialize connection
prisma.connectWithRetry().catch(error => {
  console.error('❌ Failed to initialize database connection:', error);
});

// Graceful shutdown
process.on('SIGINT', async () => {
  console.log('🛑 Received SIGINT, closing database connection...');
  await prisma.$disconnect();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('🛑 Received SIGTERM, closing database connection...');
  await prisma.$disconnect();
  process.exit(0);
});

export default prisma;
