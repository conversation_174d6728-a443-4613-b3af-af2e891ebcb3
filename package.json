{"name": "prodrank<PERSON>", "private": true, "scripts": {"build": "remix vite:build", "dev": "shopify app dev", "config:link": "shopify app config link", "generate": "shopify app generate", "deploy": "shopify app deploy", "config:use": "shopify app config use", "env": "shopify app env", "start": "remix-serve ./build/server/index.js", "docker-start": "npm run setup && npm run start", "setup": "prisma generate && prisma migrate deploy", "vercel-build": "npm run build && npm run setup", "lint": "eslint --cache --cache-location ./node_modules/.cache/eslint .", "shopify": "shopify", "prisma": "prisma", "graphql-codegen": "graphql-codegen", "vite": "vite"}, "type": "module", "engines": {"node": "^18.20 || ^20.10 || >=21.0.0"}, "dependencies": {"@emotion/is-prop-valid": "^1.3.1", "@prisma/client": "^6.2.1", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@remix-run/dev": "^2.16.1", "@remix-run/fs-routes": "^2.16.1", "@remix-run/node": "^2.16.1", "@remix-run/react": "^2.16.1", "@remix-run/serve": "^2.16.1", "@remix-run/vercel": "^1.19.3", "@shopify/app-bridge-react": "^4.1.6", "@shopify/polaris": "^12.0.0", "@shopify/shopify-app-remix": "^3.7.0", "@shopify/shopify-app-session-storage-prisma": "^6.0.0", "@tailwindcss/postcss": "^4.1.11", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.23.9", "isbot": "^5.1.0", "lottie-react": "^2.4.1", "lucide-react": "^0.526.0", "prisma": "^6.2.1", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "vite-tsconfig-paths": "^5.0.1"}, "devDependencies": {"@remix-run/eslint-config": "^2.16.1", "@remix-run/route-config": "^2.16.1", "@shopify/api-codegen-preset": "^1.1.1", "@types/eslint": "^9.6.1", "@types/node": "^22.2.0", "@types/react": "^18.2.31", "@types/react-dom": "^18.2.14", "autoprefixer": "^10.4.21", "eslint": "^8.42.0", "eslint-config-prettier": "^10.0.1", "prettier": "^3.2.4", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.6", "typescript": "^5.2.2", "vite": "^6.2.2"}, "workspaces": ["extensions/*"], "trustedDependencies": ["@shopify/plugin-cloudflare"], "resolutions": {"@graphql-tools/url-loader": "8.0.16", "@graphql-codegen/client-preset": "4.7.0", "@graphql-codegen/typescript-operations": "4.5.0", "minimatch": "9.0.5", "vite": "^6.2.2"}, "overrides": {"@graphql-tools/url-loader": "8.0.16", "@graphql-codegen/client-preset": "4.7.0", "@graphql-codegen/typescript-operations": "4.5.0", "minimatch": "9.0.5", "vite": "^6.2.2"}, "author": "Hp"}