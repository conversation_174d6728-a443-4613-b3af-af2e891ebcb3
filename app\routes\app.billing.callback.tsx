import type { LoaderFunctionArgs } from "@remix-run/node";
import { redirect } from "@remix-run/node";
import { authenticate } from "../shopify.server";
import { BillingService } from "../services/billing.server";
import { CreditsService } from "../services/credits.server";
import { invalidateBillingCache } from "../utils/cache.server";
import db from "../db.server";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  try {
    const url = new URL(request.url);
    const charge_id = url.searchParams.get("charge_id");
    const purchase_id = url.searchParams.get("purchase_id");

    console.log(`🔄 Billing callback received - charge_id: ${charge_id}, purchase_id: ${purchase_id}`);
    console.log(`🔄 Callback URL: ${request.url}`);

    // Try to authenticate with enhanced error handling
    let admin, session;
    try {
      const authResult = await authenticate.admin(request);
      admin = authResult.admin;
      session = authResult.session;
      console.log(`✅ Authentication successful for shop: ${session.shop}`);
    } catch (authError) {
      console.error("❌ Authentication failed in billing callback:", authError);

      // For billing callbacks, we need to handle this differently
      // The session might be invalidated during the Shopify billing flow
      // Let's try to use webhook-style authentication or redirect to complete auth

      // Extract shop from URL parameters
      const shopParam = url.searchParams.get("shop");

      if (shopParam) {
        console.log(`🔄 Attempting to recover billing callback for shop: ${shopParam}`);

        // Store the callback parameters for later processing
        const callbackData = {
          charge_id,
          purchase_id,
          timestamp: Date.now(),
          url: request.url,
          shop: shopParam
        };

        // Redirect to a recovery page that will re-authenticate and process the callback
        const recoveryUrl = `/app/billing/recovery?shop=${shopParam}&data=${encodeURIComponent(JSON.stringify(callbackData))}`;
        console.log(`🔄 Redirecting to recovery URL: ${recoveryUrl}`);
        throw redirect(recoveryUrl);
      } else {
        console.error("❌ No shop parameter found in billing callback");
        console.log(`🔍 Callback URL: ${request.url}`);
        console.log(`🔍 Available URL params:`, Object.fromEntries(url.searchParams.entries()));
        throw redirect("/app/billing?error=callback_failed");
      }
    }

    if (!session?.shop) {
      console.error("❌ No session or shop found in billing callback");
      throw redirect("/app/billing?error=no_session");
    }

    // Ensure session exists in database before proceeding
    const dbSession = await db.session.findUnique({
      where: { shop: session.shop }
    });

    if (!dbSession) {
      console.error(`❌ Session not found in database for shop: ${session.shop}`);
      // Create or update session to ensure it exists
      await db.session.upsert({
        where: { shop: session.shop },
        update: {
          accessToken: session.accessToken || '',
          scope: session.scope || '',
          expires: session.expires ? new Date(session.expires) : null,
          isOnline: session.isOnline || false,
          state: (session as any).state || '',
          updatedAt: new Date()
        },
        create: {
          id: session.id || `offline_${session.shop}`,
          shop: session.shop,
          state: (session as any).state || '',
          isOnline: session.isOnline || false,
          scope: session.scope || '',
          expires: session.expires ? new Date(session.expires) : null,
          accessToken: session.accessToken || '',
          userId: (session as any).userId || null,
          firstName: '',
          lastName: '',
          email: '',
          accountOwner: false,
          locale: '',
          collaborator: false,
          emailVerified: false
        }
      });
      console.log(`✅ Session created/updated for shop: ${session.shop}`);
    }

    console.log(`🔄 Processing billing callback for shop: ${session.shop}, charge_id: ${charge_id}, purchase_id: ${purchase_id}`);

    const billingService = new BillingService(admin, session.shop);
    if (charge_id) {
      // Handle subscription confirmation
      console.log(`Subscription confirmed for shop ${session.shop}, charge_id: ${charge_id}`);
      
      // Get updated subscription data
      const subscriptionData = await billingService.getCurrentSubscription();
      const activeSubscriptions = subscriptionData.data?.currentAppInstallation?.activeSubscriptions || [];
      
      if (activeSubscriptions.length > 0) {
        const subscription = activeSubscriptions[0];

        // Use database transaction for consistency
        await db.$transaction(async (tx) => {
          try {
            // Store subscription details in database
            await tx.billingSubscription.upsert({
              where: { subscriptionId: subscription.id },
              update: {
                status: subscription.status,
                planId: determinePlanId(subscription),
                trialDays: subscription.trialDays || 0,
                currentPeriodEnd: subscription.currentPeriodEnd ? new Date(subscription.currentPeriodEnd) : null,
                updatedAt: new Date()
              },
              create: {
                shop: session.shop,
                subscriptionId: subscription.id,
                status: subscription.status,
                planId: determinePlanId(subscription),
                trialDays: subscription.trialDays || 0,
                trialEndsAt: subscription.trialDays ? new Date(Date.now() + subscription.trialDays * 24 * 60 * 60 * 1000) : null,
                currentPeriodEnd: subscription.currentPeriodEnd ? new Date(subscription.currentPeriodEnd) : null,
                priceAmount: getSubscriptionPrice(subscription),
                priceCurrency: 'USD'
              }
            });

            // Verify session exists before creating billing event
            const sessionExists = await tx.session.findUnique({
              where: { shop: session.shop }
            });

            if (sessionExists) {
              // Log billing event
              await tx.billingEvent.create({
                data: {
                  shop: session.shop,
                  eventType: 'subscription_confirmed',
                  referenceId: subscription.id,
                  eventData: JSON.stringify({
                    chargeId: charge_id,
                    subscriptionStatus: subscription.status,
                    trialDays: subscription.trialDays,
                    timestamp: new Date().toISOString()
                  })
                }
              });

              // Update session with subscription info
              await tx.session.update({
                where: { shop: session.shop },
                data: {
                  subscriptionId: subscription.id,
                  subscriptionStatus: subscription.status,
                  billingPlanId: determinePlanId(subscription),
                  trialEndsAt: subscription.trialDays ? new Date(Date.now() + subscription.trialDays * 24 * 60 * 60 * 1000) : null,
                  lastBillingCheck: new Date()
                }
              });
            } else {
              console.warn(`⚠️ Session not found for shop ${session.shop}, skipping billing event and session update`);
            }

            console.log(`✅ Subscription confirmed and stored for shop ${session.shop}`);
          } catch (dbError) {
            console.error(`❌ Database error in billing callback for shop ${session.shop}:`, dbError);
            throw dbError; // Re-throw to trigger transaction rollback
          }
        });

        // Invalidate billing cache to force refresh (outside transaction)
        invalidateBillingCache(session.shop);
        console.log(`🔄 Billing cache invalidated for shop ${session.shop}`);

        // Redirect back to the app with success parameter
        return redirect("/app/billing?success=subscription");
      }
    }
    
    if (purchase_id) {
      // Handle one-time purchase confirmation
      console.log(`One-time purchase confirmed for shop ${session.shop}, purchase_id: ${purchase_id}`);
      
      // Get purchase details
      const purchaseData = await billingService.getOneTimePurchases();
      const purchases = purchaseData.data?.currentAppInstallation?.oneTimePurchases?.edges || [];
      const purchase = purchases.find((edge: any) => edge.node.id === purchase_id)?.node;
      
      if (purchase) {
        try {
          // Store purchase details in database
          await db.billingPurchase.upsert({
            where: { purchaseId: purchase.id },
            update: {
              status: purchase.status,
              amount: parseFloat(purchase.price.amount),
              productCount: Math.round(parseFloat(purchase.price.amount) / 0.10),
              description: purchase.name,
              updatedAt: new Date()
            },
            create: {
              shop: session.shop,
              purchaseId: purchase.id,
              status: purchase.status,
              amount: parseFloat(purchase.price.amount),
              productCount: Math.round(parseFloat(purchase.price.amount) / 0.10),
              currency: purchase.price.currencyCode || 'USD',
              description: purchase.name
            }
          });

          // Log billing event
          await db.billingEvent.create({
            data: {
              shop: session.shop,
              eventType: 'purchase_confirmed',
              referenceId: purchase.id,
              eventData: JSON.stringify(purchase)
            }
          });

          console.log(`✅ Purchase confirmed and stored for shop ${session.shop}`);

          // Add credits for the purchase
          const creditsService = new CreditsService(session.shop);
          await creditsService.addCredits(
            purchase.productCount || 1,
            purchase.id,
            `Purchase of ${purchase.productCount || 1} optimization credits`
          );
          console.log(`💳 Added ${purchase.productCount || 1} credits for shop ${session.shop}`);

          // Invalidate billing cache to force refresh
          invalidateBillingCache(session.shop);
          console.log(`🔄 Billing cache invalidated for shop ${session.shop}`);

        } catch (dbError) {
          console.error('Failed to store purchase confirmation in database:', dbError);
          // Don't fail the whole operation if database fails
        }

        // Redirect back to the app with success parameter
        return redirect("/app/billing?success=purchase");
      }
    }
    
    // If we get here, something went wrong
    console.error(`Billing callback failed for shop ${session.shop}. charge_id: ${charge_id}, purchase_id: ${purchase_id}`);
    return redirect("/app/billing?error=callback_failed");

  } catch (error) {
    console.error('❌ Billing callback error:', error);

    // Check if this is an authentication error
    if (error instanceof Response && error.status >= 300 && error.status < 400) {
      // This is a redirect, let it through
      throw error;
    }

    // For other errors, try to extract shop information for better error handling
    const url = new URL(request.url);
    const shopParam = url.searchParams.get("shop");
    const charge_id = url.searchParams.get("charge_id");
    const purchase_id = url.searchParams.get("purchase_id");

    console.error(`❌ Billing callback failed for shop: ${shopParam}, charge_id: ${charge_id}, purchase_id: ${purchase_id}`, error);

    // If we have shop information, redirect to billing page with error
    if (shopParam) {
      return redirect(`/app/billing?error=callback_failed&shop=${shopParam}`);
    }

    return redirect("/app/billing?error=system_error");
  }
};

// Helper function to determine plan ID from subscription data
function determinePlanId(subscription: any): string {
  const lineItem = subscription.lineItems?.[0];
  if (!lineItem) return 'unknown';
  
  const pricingDetails = lineItem.plan?.pricingDetails;
  if (!pricingDetails) return 'unknown';
  
  if (pricingDetails.interval === 'ANNUAL') {
    return 'annual';
  } else if (pricingDetails.interval === 'EVERY_30_DAYS') {
    return 'monthly';
  }
  
  return 'unknown';
}

// Helper function to get subscription price from subscription data
function getSubscriptionPrice(subscription: any): number {
  const lineItem = subscription.lineItems?.[0];
  if (!lineItem) return 0;

  const pricingDetails = lineItem.plan?.pricingDetails;
  if (!pricingDetails) return 0;

  // Return price based on plan type
  if (pricingDetails.interval === 'ANNUAL') {
    return 199.99; // Annual plan price
  } else if (pricingDetails.interval === 'EVERY_30_DAYS') {
    return 19.99; // Monthly plan price
  }

  return 0;
}
