{"buildManifest": {"serverBundles": {"nodejs-eyJydW50aW1lIjoibm9kZWpzIn0": {"id": "nodejs-eyJydW50aW1lIjoibm9kZWpzIn0", "file": "build/server/nodejs-eyJydW50aW1lIjoibm9kZWpzIn0/index.js", "config": {"runtime": "nodejs"}}}, "routeIdToServerBundleId": {"routes/webhooks.app_purchases_one_time.update": "nodejs-eyJydW50aW1lIjoibm9kZWpzIn0", "routes/webhooks.app_subscriptions.update": "nodejs-eyJydW50aW1lIjoibm9kZWpzIn0", "routes/webhooks.app.scopes_update": "nodejs-eyJydW50aW1lIjoibm9kZWpzIn0", "routes/webhooks.app.uninstalled": "nodejs-eyJydW50aW1lIjoibm9kZWpzIn0", "routes/auth.login": "nodejs-eyJydW50aW1lIjoibm9kZWpzIn0", "routes/auth.$": "nodejs-eyJydW50aW1lIjoibm9kZWpzIn0", "routes/_index": "nodejs-eyJydW50aW1lIjoibm9kZWpzIn0", "routes/app.api.sync-subscription": "nodejs-eyJydW50aW1lIjoibm9kZWpzIn0", "routes/app.api.billing-refresh": "nodejs-eyJydW50aW1lIjoibm9kZWpzIn0", "routes/app.api.billing-status": "nodejs-eyJydW50aW1lIjoibm9kZWpzIn0", "routes/app.seo-dashboard": "nodejs-eyJydW50aW1lIjoibm9kZWpzIn0", "routes/app.api.products": "nodejs-eyJydW50aW1lIjoibm9kZWpzIn0", "routes/app.additional": "nodejs-eyJydW50aW1lIjoibm9kZWpzIn0", "routes/app.settings": "nodejs-eyJydW50aW1lIjoibm9kZWpzIn0", "routes/app.billing": "nodejs-eyJydW50aW1lIjoibm9kZWpzIn0", "routes/app.billing.pay-per-use": "nodejs-eyJydW50aW1lIjoibm9kZWpzIn0", "routes/app.billing.callback": "nodejs-eyJydW50aW1lIjoibm9kZWpzIn0", "routes/app.billing.recovery": "nodejs-eyJydW50aW1lIjoibm9kZWpzIn0", "routes/app.billing.pricing": "nodejs-eyJydW50aW1lIjoibm9kZWpzIn0", "routes/app.review": "nodejs-eyJydW50aW1lIjoibm9kZWpzIn0", "routes/app._index": "nodejs-eyJydW50aW1lIjoibm9kZWpzIn0"}, "routes": {"root": {"path": "", "id": "root", "file": "app/root.tsx", "config": {}}, "routes/webhooks.app_purchases_one_time.update": {"id": "routes/webhooks.app_purchases_one_time.update", "parentId": "root", "file": "app/routes/webhooks.app_purchases_one_time.update.tsx", "path": "webhooks/app_purchases_one_time/update", "config": {"runtime": "nodejs"}}, "routes/webhooks.app_subscriptions.update": {"id": "routes/webhooks.app_subscriptions.update", "parentId": "root", "file": "app/routes/webhooks.app_subscriptions.update.tsx", "path": "webhooks/app_subscriptions/update", "config": {"runtime": "nodejs"}}, "routes/webhooks.app.scopes_update": {"id": "routes/webhooks.app.scopes_update", "parentId": "root", "file": "app/routes/webhooks.app.scopes_update.tsx", "path": "webhooks/app/scopes_update", "config": {"runtime": "nodejs"}}, "routes/webhooks.app.uninstalled": {"id": "routes/webhooks.app.uninstalled", "parentId": "root", "file": "app/routes/webhooks.app.uninstalled.tsx", "path": "webhooks/app/uninstalled", "config": {"runtime": "nodejs"}}, "routes/auth.login": {"id": "routes/auth.login", "parentId": "root", "file": "app/routes/auth.login/route.tsx", "path": "auth/login", "config": {"runtime": "nodejs"}}, "routes/auth.$": {"id": "routes/auth.$", "parentId": "root", "file": "app/routes/auth.$.tsx", "path": "auth/*", "config": {"runtime": "nodejs"}}, "routes/_index": {"id": "routes/_index", "parentId": "root", "file": "app/routes/_index/route.tsx", "index": true, "config": {"runtime": "nodejs"}}, "routes/app": {"id": "routes/app", "parentId": "root", "file": "app/routes/app.tsx", "path": "app", "config": {}}, "routes/app.api.sync-subscription": {"id": "routes/app.api.sync-subscription", "parentId": "routes/app", "file": "app/routes/app.api.sync-subscription.tsx", "path": "api/sync-subscription", "config": {"runtime": "nodejs"}}, "routes/app.api.billing-refresh": {"id": "routes/app.api.billing-refresh", "parentId": "routes/app", "file": "app/routes/app.api.billing-refresh.tsx", "path": "api/billing-refresh", "config": {"runtime": "nodejs"}}, "routes/app.api.billing-status": {"id": "routes/app.api.billing-status", "parentId": "routes/app", "file": "app/routes/app.api.billing-status.tsx", "path": "api/billing-status", "config": {"runtime": "nodejs"}}, "routes/app.seo-dashboard": {"id": "routes/app.seo-dashboard", "parentId": "routes/app", "file": "app/routes/app.seo-dashboard.tsx", "path": "seo-dashboard", "config": {"runtime": "nodejs"}}, "routes/app.api.products": {"id": "routes/app.api.products", "parentId": "routes/app", "file": "app/routes/app.api.products.tsx", "path": "api/products", "config": {"runtime": "nodejs"}}, "routes/app.additional": {"id": "routes/app.additional", "parentId": "routes/app", "file": "app/routes/app.additional.tsx", "path": "additional", "config": {"runtime": "nodejs"}}, "routes/app.settings": {"id": "routes/app.settings", "parentId": "routes/app", "file": "app/routes/app.settings.tsx", "path": "settings", "config": {"runtime": "nodejs"}}, "routes/app.billing": {"id": "routes/app.billing", "parentId": "routes/app", "file": "app/routes/app.billing.tsx", "path": "billing", "config": {"runtime": "nodejs"}}, "routes/app.billing.pay-per-use": {"id": "routes/app.billing.pay-per-use", "parentId": "routes/app.billing", "file": "app/routes/app.billing.pay-per-use.tsx", "path": "pay-per-use", "config": {}}, "routes/app.billing.callback": {"id": "routes/app.billing.callback", "parentId": "routes/app.billing", "file": "app/routes/app.billing.callback.tsx", "path": "callback", "config": {}}, "routes/app.billing.recovery": {"id": "routes/app.billing.recovery", "parentId": "routes/app.billing", "file": "app/routes/app.billing.recovery.tsx", "path": "recovery", "config": {}}, "routes/app.billing.pricing": {"id": "routes/app.billing.pricing", "parentId": "routes/app.billing", "file": "app/routes/app.billing.pricing.tsx", "path": "pricing", "config": {}}, "routes/app.review": {"id": "routes/app.review", "parentId": "routes/app", "file": "app/routes/app.review.tsx", "path": "review", "config": {"runtime": "nodejs"}}, "routes/app._index": {"id": "routes/app._index", "parentId": "routes/app", "file": "app/routes/app._index.tsx", "index": true, "config": {"runtime": "nodejs"}}}}, "remixConfig": {"appDirectory": "C:\\seo-bulk-easy\\app", "basename": "/", "buildDirectory": "C:\\seo-bulk-easy\\build", "future": {"v3_fetcherPersist": true, "v3_relativeSplatPath": true, "v3_throwAbortReason": true, "v3_routeConfig": true, "v3_singleFetch": false, "v3_lazyRouteDiscovery": true, "unstable_optimizeDeps": false}, "manifest": false, "publicPath": "/", "routes": {"root": {"path": "", "id": "root", "file": "root.tsx"}, "routes/webhooks.app_purchases_one_time.update": {"id": "routes/webhooks.app_purchases_one_time.update", "parentId": "root", "file": "routes/webhooks.app_purchases_one_time.update.tsx", "path": "webhooks/app_purchases_one_time/update"}, "routes/webhooks.app_subscriptions.update": {"id": "routes/webhooks.app_subscriptions.update", "parentId": "root", "file": "routes/webhooks.app_subscriptions.update.tsx", "path": "webhooks/app_subscriptions/update"}, "routes/webhooks.app.scopes_update": {"id": "routes/webhooks.app.scopes_update", "parentId": "root", "file": "routes/webhooks.app.scopes_update.tsx", "path": "webhooks/app/scopes_update"}, "routes/webhooks.app.uninstalled": {"id": "routes/webhooks.app.uninstalled", "parentId": "root", "file": "routes/webhooks.app.uninstalled.tsx", "path": "webhooks/app/uninstalled"}, "routes/auth.login": {"id": "routes/auth.login", "parentId": "root", "file": "routes/auth.login/route.tsx", "path": "auth/login"}, "routes/auth.$": {"id": "routes/auth.$", "parentId": "root", "file": "routes/auth.$.tsx", "path": "auth/*"}, "routes/_index": {"id": "routes/_index", "parentId": "root", "file": "routes/_index/route.tsx", "index": true}, "routes/app": {"id": "routes/app", "parentId": "root", "file": "routes/app.tsx", "path": "app"}, "routes/app.api.sync-subscription": {"id": "routes/app.api.sync-subscription", "parentId": "routes/app", "file": "routes/app.api.sync-subscription.tsx", "path": "api/sync-subscription"}, "routes/app.api.billing-refresh": {"id": "routes/app.api.billing-refresh", "parentId": "routes/app", "file": "routes/app.api.billing-refresh.tsx", "path": "api/billing-refresh"}, "routes/app.api.billing-status": {"id": "routes/app.api.billing-status", "parentId": "routes/app", "file": "routes/app.api.billing-status.tsx", "path": "api/billing-status"}, "routes/app.seo-dashboard": {"id": "routes/app.seo-dashboard", "parentId": "routes/app", "file": "routes/app.seo-dashboard.tsx", "path": "seo-dashboard"}, "routes/app.api.products": {"id": "routes/app.api.products", "parentId": "routes/app", "file": "routes/app.api.products.tsx", "path": "api/products"}, "routes/app.additional": {"id": "routes/app.additional", "parentId": "routes/app", "file": "routes/app.additional.tsx", "path": "additional"}, "routes/app.settings": {"id": "routes/app.settings", "parentId": "routes/app", "file": "routes/app.settings.tsx", "path": "settings"}, "routes/app.billing": {"id": "routes/app.billing", "parentId": "routes/app", "file": "routes/app.billing.tsx", "path": "billing"}, "routes/app.billing.pay-per-use": {"id": "routes/app.billing.pay-per-use", "parentId": "routes/app.billing", "file": "routes/app.billing.pay-per-use.tsx", "path": "pay-per-use"}, "routes/app.billing.callback": {"id": "routes/app.billing.callback", "parentId": "routes/app.billing", "file": "routes/app.billing.callback.tsx", "path": "callback"}, "routes/app.billing.recovery": {"id": "routes/app.billing.recovery", "parentId": "routes/app.billing", "file": "routes/app.billing.recovery.tsx", "path": "recovery"}, "routes/app.billing.pricing": {"id": "routes/app.billing.pricing", "parentId": "routes/app.billing", "file": "routes/app.billing.pricing.tsx", "path": "pricing"}, "routes/app.review": {"id": "routes/app.review", "parentId": "routes/app", "file": "routes/app.review.tsx", "path": "review"}, "routes/app._index": {"id": "routes/app._index", "parentId": "routes/app", "file": "routes/app._index.tsx", "index": true}}, "serverBuildFile": "index.js", "serverModuleFormat": "esm", "ssr": true}, "viteConfig": {"build": {"assetsDir": "assets"}}}