import "@shopify/shopify-app-remix/adapters/node";
import {
  ApiVersion,
  AppDistribution,
  DeliveryMethod,
  shopifyApp,
} from "@shopify/shopify-app-remix/server";
import { PrismaSessionStorage } from "@shopify/shopify-app-session-storage-prisma";
import { restResources } from "@shopify/shopify-api/rest/admin/2025-01";
import prisma from "./db.server";

// Enhanced session storage with proper cleanup and error handling
class EnhancedPrismaSessionStorage extends PrismaSessionStorage<any> {
  constructor(prisma: any) {
    super(prisma);
  }

  // Override to add session cleanup and better error handling
  async storeSession(session: any): Promise<boolean> {
    try {
      // Clean up old sessions for this shop before storing new one
      await this.cleanupOldSessions(session.shop);

      // Store the session with enhanced error handling
      const result = await super.storeSession(session);

      if (result) {
        console.log(`✅ Session stored successfully for shop: ${session.shop}`);
      }

      return result;
    } catch (error) {
      console.error('❌ Error storing session:', error);
      return false;
    }
  }

  // Enhanced session loading with validation
  async loadSession(id: string): Promise<any> {
    try {
      const session = await super.loadSession(id);

      if (session) {
        // Validate session is not expired (sessions should be refreshed within 24 hours)
        const now = new Date();
        const sessionDate = new Date(session.expires || Date.now());

        if (sessionDate < now) {
          console.log(`⚠️ Session expired for shop: ${session.shop}, removing...`);
          await super.deleteSession(id);
          return undefined;
        }

        console.log(`✅ Session loaded successfully for shop: ${session.shop}`);
      }

      return session;
    } catch (error) {
      console.error('❌ Error loading session:', error);
      return undefined;
    }
  }

  // Clean up sessions older than 24 hours for the same shop
  private async cleanupOldSessions(shop: string): Promise<void> {
    try {
      const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);

      // Use raw query since Prisma schema might not have updatedAt
      const deletedSessions = await prisma.$executeRaw`
        DELETE FROM Session
        WHERE shop = ${shop}
        AND expires < ${oneDayAgo}
      `;

      if (deletedSessions > 0) {
        console.log(`🧹 Cleaned up ${deletedSessions} old sessions for shop: ${shop}`);
      }
    } catch (error) {
      console.error('❌ Error cleaning up old sessions:', error);
    }
  }
}

const shopify = shopifyApp({
  apiKey: process.env.SHOPIFY_API_KEY,
  apiSecretKey: process.env.SHOPIFY_API_SECRET || "",
  apiVersion: ApiVersion.January25,
  scopes: process.env.SCOPES?.split(","),
  appUrl: process.env.SHOPIFY_APP_URL || "",
  authPathPrefix: "/auth",
  sessionStorage: new EnhancedPrismaSessionStorage(prisma),
  distribution: AppDistribution.AppStore,
  restResources,
  future: {
    unstable_newEmbeddedAuthStrategy: true,
  },
  // Enhanced webhook configuration
  webhooks: {
    APP_UNINSTALLED: {
      deliveryMethod: DeliveryMethod.Http,
      callbackUrl: "/webhooks/app/uninstalled",
    },
    APP_SUBSCRIPTIONS_UPDATE: {
      deliveryMethod: DeliveryMethod.Http,
      callbackUrl: "/webhooks/app_subscriptions/update",
    },
    APP_PURCHASES_ONE_TIME_UPDATE: {
      deliveryMethod: DeliveryMethod.Http,
      callbackUrl: "/webhooks/app_purchases_one_time/update",
    },
    APP_SCOPES_UPDATE: {
      deliveryMethod: DeliveryMethod.Http,
      callbackUrl: "/webhooks/app/scopes_update",
    },
  },
  // Enhanced authentication configuration
  auth: {
    path: "/auth",
    callbackPath: "/auth/callback",
  },
  ...(process.env.SHOP_CUSTOM_DOMAIN
    ? { customShopDomains: [process.env.SHOP_CUSTOM_DOMAIN] }
    : {}),
});

export default shopify;
export const apiVersion = ApiVersion.January25;
export const addDocumentResponseHeaders = shopify.addDocumentResponseHeaders;
export const authenticate = shopify.authenticate;
export const unauthenticated = shopify.unauthenticated;
export const login = shopify.login;
export const registerWebhooks = shopify.registerWebhooks;
export const sessionStorage = shopify.sessionStorage;
