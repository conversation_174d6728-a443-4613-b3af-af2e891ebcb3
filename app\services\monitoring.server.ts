/**
 * Monitoring and Logging Service
 * Comprehensive monitoring, logging, and alerting for production deployment
 */

import { performance } from "perf_hooks";
import db from "../db.server";
import { createError, logError, type ErrorContext } from "../utils/error-handling.server";
import { performanceService } from "./performance.server";

export interface LogEntry {
  id: string;
  level: 'debug' | 'info' | 'warn' | 'error' | 'critical';
  message: string;
  timestamp: Date;
  source: string;
  context?: Record<string, any>;
  stack?: string;
  userId?: string;
  shop?: string;
  requestId?: string;
}

export interface HealthCheck {
  service: string;
  status: 'healthy' | 'degraded' | 'unhealthy';
  responseTime: number;
  message?: string;
  details?: Record<string, any>;
  timestamp: Date;
}

export interface SystemMetrics {
  timestamp: Date;
  memory: {
    used: number;
    total: number;
    percentage: number;
  };
  cpu: {
    usage: number;
    loadAverage: number[];
  };
  disk: {
    used: number;
    total: number;
    percentage: number;
  };
  network: {
    bytesIn: number;
    bytesOut: number;
  };
  database: {
    connections: number;
    queryTime: number;
    errorRate: number;
  };
  cache: {
    hitRate: number;
    memoryUsage: number;
    entries: number;
  };
}

export interface Alert {
  id: string;
  type: 'performance' | 'error' | 'security' | 'business';
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  message: string;
  source: string;
  timestamp: Date;
  resolved: boolean;
  resolvedAt?: Date;
  metadata?: Record<string, any>;
}

/**
 * Comprehensive monitoring service
 */
export class MonitoringService {
  private static instance: MonitoringService;
  private logs: LogEntry[] = [];
  private alerts: Alert[] = [];
  private healthChecks: Map<string, HealthCheck> = new Map();
  private maxLogEntries = 10000;
  private maxAlerts = 1000;

  static getInstance(): MonitoringService {
    if (!MonitoringService.instance) {
      MonitoringService.instance = new MonitoringService();
    }
    return MonitoringService.instance;
  }

  /**
   * Log message with structured data
   */
  log(
    level: LogEntry['level'],
    message: string,
    context?: {
      source?: string;
      userId?: string;
      shop?: string;
      requestId?: string;
      metadata?: Record<string, any>;
      error?: Error;
    }
  ): void {
    const logEntry: LogEntry = {
      id: `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      level,
      message,
      timestamp: new Date(),
      source: context?.source || 'application',
      context: context?.metadata,
      userId: context?.userId,
      shop: context?.shop,
      requestId: context?.requestId,
      stack: context?.error?.stack
    };

    this.logs.push(logEntry);

    // Limit log entries in memory
    if (this.logs.length > this.maxLogEntries) {
      this.logs = this.logs.slice(-Math.floor(this.maxLogEntries * 0.8));
    }

    // Console output with formatting
    this.outputToConsole(logEntry);

    // Store critical logs in database
    if (level === 'error' || level === 'critical') {
      this.persistLog(logEntry).catch(error => {
        console.error('Failed to persist log:', error);
      });
    }

    // Create alerts for critical issues
    if (level === 'critical') {
      this.createAlert({
        type: 'error',
        severity: 'critical',
        title: 'Critical Error Detected',
        message,
        source: logEntry.source,
        metadata: logEntry.context
      });
    }
  }

  /**
   * Convenience logging methods
   */
  debug(message: string, context?: any): void {
    this.log('debug', message, context);
  }

  info(message: string, context?: any): void {
    this.log('info', message, context);
  }

  warn(message: string, context?: any): void {
    this.log('warn', message, context);
  }

  error(message: string, context?: any): void {
    this.log('error', message, context);
  }

  critical(message: string, context?: any): void {
    this.log('critical', message, context);
  }

  /**
   * Health check management
   */
  async performHealthCheck(serviceName: string, checkFunction: () => Promise<any>): Promise<HealthCheck> {
    const startTime = performance.now();
    
    try {
      const result = await checkFunction();
      const responseTime = performance.now() - startTime;

      const healthCheck: HealthCheck = {
        service: serviceName,
        status: 'healthy',
        responseTime,
        timestamp: new Date(),
        details: result
      };

      this.healthChecks.set(serviceName, healthCheck);
      return healthCheck;

    } catch (error) {
      const responseTime = performance.now() - startTime;
      const healthCheck: HealthCheck = {
        service: serviceName,
        status: 'unhealthy',
        responseTime,
        message: error instanceof Error ? error.message : 'Health check failed',
        timestamp: new Date()
      };

      this.healthChecks.set(serviceName, healthCheck);
      
      this.createAlert({
        type: 'performance',
        severity: 'high',
        title: `Service Health Check Failed: ${serviceName}`,
        message: healthCheck.message || 'Unknown error',
        source: 'health-check'
      });

      return healthCheck;
    }
  }

  /**
   * Get all health checks
   */
  getAllHealthChecks(): HealthCheck[] {
    return Array.from(this.healthChecks.values());
  }

  /**
   * Get system health overview
   */
  async getSystemHealth(): Promise<{
    overall: 'healthy' | 'degraded' | 'unhealthy';
    services: HealthCheck[];
    metrics: SystemMetrics;
    alerts: Alert[];
  }> {
    // Perform core health checks
    const healthChecks = await Promise.all([
      this.performHealthCheck('database', this.checkDatabase),
      this.performHealthCheck('cache', this.checkCache),
      this.performHealthCheck('memory', this.checkMemory),
      this.performHealthCheck('disk', this.checkDisk)
    ]);

    // Determine overall health
    const unhealthyServices = healthChecks.filter(hc => hc.status === 'unhealthy');
    const degradedServices = healthChecks.filter(hc => hc.status === 'degraded');

    let overall: 'healthy' | 'degraded' | 'unhealthy';
    if (unhealthyServices.length > 0) {
      overall = 'unhealthy';
    } else if (degradedServices.length > 0) {
      overall = 'degraded';
    } else {
      overall = 'healthy';
    }

    return {
      overall,
      services: healthChecks,
      metrics: await this.collectSystemMetrics(),
      alerts: this.getActiveAlerts()
    };
  }

  /**
   * Create alert
   */
  createAlert(alert: Omit<Alert, 'id' | 'timestamp' | 'resolved'>): string {
    const newAlert: Alert = {
      id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
      resolved: false,
      ...alert
    };

    this.alerts.push(newAlert);

    // Limit alerts in memory
    if (this.alerts.length > this.maxAlerts) {
      this.alerts = this.alerts.slice(-Math.floor(this.maxAlerts * 0.8));
    }

    // Log the alert
    this.log('warn', `Alert created: ${alert.title}`, {
      source: 'monitoring',
      metadata: { alertId: newAlert.id, severity: alert.severity }
    });

    return newAlert.id;
  }

  /**
   * Resolve alert
   */
  resolveAlert(alertId: string): boolean {
    const alert = this.alerts.find(a => a.id === alertId);
    if (alert && !alert.resolved) {
      alert.resolved = true;
      alert.resolvedAt = new Date();
      
      this.log('info', `Alert resolved: ${alert.title}`, {
        source: 'monitoring',
        metadata: { alertId }
      });
      
      return true;
    }
    return false;
  }

  /**
   * Get active alerts
   */
  getActiveAlerts(): Alert[] {
    return this.alerts.filter(alert => !alert.resolved);
  }

  /**
   * Get logs with filtering
   */
  getLogs(filters?: {
    level?: LogEntry['level'];
    source?: string;
    shop?: string;
    limit?: number;
    since?: Date;
  }): LogEntry[] {
    let filteredLogs = [...this.logs];

    if (filters?.level) {
      filteredLogs = filteredLogs.filter(log => log.level === filters.level);
    }

    if (filters?.source) {
      filteredLogs = filteredLogs.filter(log => log.source === filters.source);
    }

    if (filters?.shop) {
      filteredLogs = filteredLogs.filter(log => log.shop === filters.shop);
    }

    if (filters?.since) {
      filteredLogs = filteredLogs.filter(log => log.timestamp >= filters.since!);
    }

    // Sort by timestamp (newest first)
    filteredLogs.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());

    if (filters?.limit) {
      filteredLogs = filteredLogs.slice(0, filters.limit);
    }

    return filteredLogs;
  }

  /**
   * Private helper methods
   */
  private outputToConsole(logEntry: LogEntry): void {
    const timestamp = logEntry.timestamp.toISOString();
    const level = logEntry.level.toUpperCase().padEnd(8);
    const source = logEntry.source.padEnd(12);
    
    let message = `[${timestamp}] ${level} ${source} ${logEntry.message}`;
    
    if (logEntry.context) {
      message += ` ${JSON.stringify(logEntry.context)}`;
    }

    switch (logEntry.level) {
      case 'debug':
        console.debug(message);
        break;
      case 'info':
        console.info(message);
        break;
      case 'warn':
        console.warn(message);
        break;
      case 'error':
      case 'critical':
        console.error(message);
        if (logEntry.stack) {
          console.error(logEntry.stack);
        }
        break;
    }
  }

  private async persistLog(logEntry: LogEntry): Promise<void> {
    try {
      // In a real implementation, you might store logs in:
      // - Database table
      // - External logging service (e.g., CloudWatch, Datadog)
      // - File system
      console.log(`💾 Persisting critical log: ${logEntry.id}`);
    } catch (error) {
      console.error('Failed to persist log:', error);
    }
  }

  private async checkDatabase(): Promise<any> {
    const startTime = performance.now();
    await db.$queryRaw`SELECT 1`;
    const queryTime = performance.now() - startTime;
    
    return {
      queryTime: Math.round(queryTime),
      status: queryTime < 100 ? 'healthy' : 'degraded'
    };
  }

  private async checkCache(): Promise<any> {
    // Would check cache service if available
    return { status: 'healthy' };
  }

  private async checkMemory(): Promise<any> {
    const memoryUsage = process.memoryUsage();
    const usedMB = memoryUsage.heapUsed / 1024 / 1024;
    const totalMB = memoryUsage.heapTotal / 1024 / 1024;
    const percentage = (usedMB / totalMB) * 100;

    return {
      usedMB: Math.round(usedMB),
      totalMB: Math.round(totalMB),
      percentage: Math.round(percentage),
      status: percentage > 90 ? 'unhealthy' : percentage > 70 ? 'degraded' : 'healthy'
    };
  }

  private async checkDisk(): Promise<any> {
    // Would check disk usage if available
    return { status: 'healthy' };
  }

  private async collectSystemMetrics(): Promise<SystemMetrics> {
    const memoryUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();

    return {
      timestamp: new Date(),
      memory: {
        used: memoryUsage.heapUsed,
        total: memoryUsage.heapTotal,
        percentage: (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100
      },
      cpu: {
        usage: (cpuUsage.user + cpuUsage.system) / 1000000, // Convert to seconds
        loadAverage: process.platform !== 'win32' ? require('os').loadavg() : [0, 0, 0]
      },
      disk: {
        used: 0, // Would implement disk usage check
        total: 0,
        percentage: 0
      },
      network: {
        bytesIn: 0, // Would implement network stats
        bytesOut: 0
      },
      database: {
        connections: 0, // Would get from database
        queryTime: 0,
        errorRate: 0
      },
      cache: {
        hitRate: 0, // Would get from cache service
        memoryUsage: 0,
        entries: 0
      }
    };
  }
}

// Export singleton instance
export const monitoringService = MonitoringService.getInstance();

// Override console methods to capture logs
const originalConsole = {
  log: console.log,
  info: console.info,
  warn: console.warn,
  error: console.error
};

console.info = (...args) => {
  monitoringService.info(args.join(' '));
  originalConsole.info(...args);
};

console.warn = (...args) => {
  monitoringService.warn(args.join(' '));
  originalConsole.warn(...args);
};

console.error = (...args) => {
  monitoringService.error(args.join(' '));
  originalConsole.error(...args);
};
